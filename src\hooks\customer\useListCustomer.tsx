import { ICustomersResponse } from '~/dto/customer.dto'

const DUMMY_DATA: ICustomersResponse = {
  data: [
    {
      id: '1',
      code: 'KH001',
      name: 'Công ty TNHH ABC',
      shortName: 'ABC',
      phone: '028 1234 5678',
      email: '<EMAIL>',
      address: '123 <PERSON><PERSON><PERSON><PERSON>, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Nguyễn Văn A',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Bán lẻ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T08:00:00Z',
      updatedAt: '2024-03-20T08:00:00Z',
      website: 'www.abc.com',
      taxCode: '0123456789',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-20T08:00:00Z',
      note: '<PERSON><PERSON><PERSON><PERSON> hàng tiềm năng',
      sapCode: 'SAP001'
    },
    {
      id: '2',
      code: 'KH002',
      name: '<PERSON>ông ty TNHH XYZ',
      shortName: 'XYZ',
      phone: '028 8765 4321',
      email: '<EMAIL>',
      address: '456 Lê Lợi, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Trần Thị B',
      department: 'Phòng Kinh doanh',
      ranking: 'B',
      source: 'Facebook',
      industry: 'Dịch vụ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-19T08:00:00Z',
      updatedAt: '2024-03-19T08:00:00Z',
      website: 'www.xyz.com',
      taxCode: '9876543210',
      market: 'domestic',
      customerGroup: 'Nhóm B',
      visitDate: '2024-03-19T08:00:00Z',
      note: 'Khách hàng thường xuyên',
      sapCode: 'SAP002'
    },
    {
      id: '3',
      code: 'KH003',
      name: 'Công ty TNHH DEF',
      shortName: 'DEF',
      phone: '028 1111 2222',
      email: '<EMAIL>',
      address: '789 Đồng Khởi, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Lê Văn C',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Google',
      industry: 'Bán sỉ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-18T08:00:00Z',
      updatedAt: '2024-03-18T08:00:00Z',
      website: 'www.def.com',
      taxCode: '1112223334',
      market: 'domestic',
      customerGroup: 'Nhóm C',
      visitDate: '2024-03-18T08:00:00Z',
      note: 'Khách hàng mới',
      sapCode: 'SAP003'
    },
    {
      id: '4',
      code: 'KH004',
      name: 'Công ty TNHH GHI',
      shortName: 'GHI',
      phone: '028 3333 4444',
      email: '<EMAIL>',
      address: '321 Nguyễn Du, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Phạm Thị D',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'LinkedIn',
      industry: 'Bán lẻ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-17T08:00:00Z',
      updatedAt: '2024-03-17T08:00:00Z',
      website: 'www.ghi.com',
      taxCode: '3334445556',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-17T08:00:00Z',
      note: 'Khách hàng VIP',
      sapCode: 'SAP004'
    },
    {
      id: '5',
      code: 'KH005',
      name: 'Công ty TNHH JKL',
      shortName: 'JKL',
      phone: '028 5555 6666',
      email: '<EMAIL>',
      address: '654 Lê Duẩn, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Hoàng Văn E',
      department: 'Phòng Kinh doanh',
      ranking: 'B',
      source: 'Website',
      industry: 'Dịch vụ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-16T08:00:00Z',
      updatedAt: '2024-03-16T08:00:00Z',
      website: 'www.jkl.com',
      taxCode: '5556667778',
      market: 'domestic',
      customerGroup: 'Nhóm B',
      visitDate: '2024-03-16T08:00:00Z',
      note: 'Khách hàng tiềm năng',
      sapCode: 'SAP005'
    },
    {
      id: '6',
      code: 'KH006',
      name: 'Công ty TNHH MNO',
      shortName: 'MNO',
      phone: '028 7777 8888',
      email: '<EMAIL>',
      address: '987 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Vũ Thị F',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Facebook',
      industry: 'Bán sỉ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-15T08:00:00Z',
      updatedAt: '2024-03-15T08:00:00Z',
      website: 'www.mno.com',
      taxCode: '7778889990',
      market: 'domestic',
      customerGroup: 'Nhóm C',
      visitDate: '2024-03-15T08:00:00Z',
      note: 'Khách hàng mới',
      sapCode: 'SAP006'
    },
    {
      id: '7',
      code: 'KH007',
      name: 'Công ty TNHH PQR',
      shortName: 'PQR',
      phone: '028 9999 0000',
      email: '<EMAIL>',
      address: '147 Lê Lợi, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Đỗ Văn G',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Google',
      industry: 'Bán lẻ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-14T08:00:00Z',
      updatedAt: '2024-03-14T08:00:00Z',
      website: 'www.pqr.com',
      taxCode: '9990001112',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-14T08:00:00Z',
      note: 'Khách hàng VIP',
      sapCode: 'SAP007'
    },
    {
      id: '8',
      code: 'KH008',
      name: 'Công ty TNHH STU',
      shortName: 'STU',
      phone: '028 2222 3333',
      email: '<EMAIL>',
      address: '258 Đồng Khởi, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Ngô Thị H',
      department: 'Phòng Kinh doanh',
      ranking: 'B',
      source: 'LinkedIn',
      industry: 'Dịch vụ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-13T08:00:00Z',
      updatedAt: '2024-03-13T08:00:00Z',
      website: 'www.stu.com',
      taxCode: '2223334445',
      market: 'domestic',
      customerGroup: 'Nhóm B',
      visitDate: '2024-03-13T08:00:00Z',
      note: 'Khách hàng thường xuyên',
      sapCode: 'SAP008'
    },
    {
      id: '9',
      code: 'KH009',
      name: 'Công ty TNHH VWX',
      shortName: 'VWX',
      phone: '028 4444 5555',
      email: '<EMAIL>',
      address: '369 Nguyễn Du, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Trịnh Văn I',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Bán sỉ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-12T08:00:00Z',
      updatedAt: '2024-03-12T08:00:00Z',
      website: 'www.vwx.com',
      taxCode: '4445556667',
      market: 'domestic',
      customerGroup: 'Nhóm C',
      visitDate: '2024-03-12T08:00:00Z',
      note: 'Khách hàng mới',
      sapCode: 'SAP009'
    },
    {
      id: '10',
      code: 'KH010',
      name: 'Công ty TNHH YZ',
      shortName: 'YZ',
      phone: '028 6666 7777',
      email: '<EMAIL>',
      address: '741 Lê Duẩn, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Lý Thị J',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Facebook',
      industry: 'Bán lẻ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-11T08:00:00Z',
      updatedAt: '2024-03-11T08:00:00Z',
      website: 'www.yz.com',
      taxCode: '6667778889',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-11T08:00:00Z',
      note: 'Khách hàng VIP',
      sapCode: 'SAP010'
    }
  ],
  total: 10
}

export const useListCustomer = () => {
  return {
    data: DUMMY_DATA,
    total: 10,
    isLoading: false,
    isError: false
  }
}
