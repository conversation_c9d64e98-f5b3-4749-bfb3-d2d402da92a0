import { useState, FC, useEffect } from 'react'
import { Badge, Button, Col, Row, Tooltip } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useTranslation } from 'react-i18next'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { ICustomer } from '~/dto/customer.dto'
import { useListCustomer } from '~/hooks/customer/useListCustomer'
import DetailButton from './component/DetailButton'
import EditButton from './component/EditButton'
import { BaseButton } from '~/components'
import { DeleteOutlined } from '@ant-design/icons'
import { CustomerData } from './grouped_data'

interface IFilterCustomer {
  pageIndex: number
  pageSize: number
}

type IProps = {}

const ListCustomerView: FC<IProps> = () => {
  const { t } = useTranslation()
  const fakeCustomerData = new CustomerData()
  const [filter, setFilter] = useState<IFilterCustomer>({
    pageIndex: 1,
    pageSize: 10
  })
  const [data, setData] = useState<any>()
  const [selectedStage, setSelectedStage] = useState<string | null>(null)

  const handleClick = (stage: string, data: any) => {
    setSelectedStage(stage)
    handleFilterChange(data)
  }

  // const { data, isLoading, total } = useListCustomer()

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const handleDelete = (record: ICustomer) => {
    console.log(record)
  }

  const handleFilterChange = (newData) => {
    setData(newData)
  }

  const columns: ColumnsType<ICustomer> = [
    {
      title: t('customer:customer.columns.stt'),
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: t('customer:customer.columns.code'),
      dataIndex: 'code',
      key: 'code',
      width: 120,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.name'),
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.phone'),
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.address'),
      dataIndex: 'address',
      key: 'address',
      width: 250,
      align: 'center'
    },
    // {
    //   title: t('customer:customer.columns.customerType'),
    //   dataIndex: 'customerType',
    //   key: 'customerType',
    //   width: 250,
    //   align: 'center'
    // },
    {
      title: t('customer:customer.columns.salesRep'),
      dataIndex: 'salesRep',
      key: 'salesRep',
      width: 250,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.department'),
      dataIndex: 'department',
      key: 'department',
      width: 250,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.ranking'),
      dataIndex: 'ranking',
      key: 'ranking',
      width: 280,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.source'),
      dataIndex: 'source',
      key: 'source',
      width: 250,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.industry'),
      dataIndex: 'industry',
      key: 'industry',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.region'),
      dataIndex: 'region',
      key: 'region',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.createdBy'),
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      align: 'center',
      render: (value: string) => (value ? new Date(value).toLocaleDateString('vi-VN') : '-')
    },
    {
      title: t('customer:customer.columns.action'),
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (value: any, record: any, index: number) => {
        return (
          <>
            <EditButton data={value} />
            <DetailButton data={record} />
            <BaseButton
              danger
              type='primary'
              shape='circle'
              icon={<DeleteOutlined />}
              tooltip='Delete'
              onClick={() => handleDelete(record)}
            />
          </>
        )
      }
    }
  ]

  const stages = [
    {
      key: 'undefinedCustomers',
      label: 'Khách hàng chưa định danh',
      color: '#00ccff',
      tooltip: 'Khách hàng chưa có đầy đủ thông tin cơ bản'
    },
    {
      key: 'potentialCustomers',
      label: 'Khách hàng tiềm năng',
      color: '#b3b300',
      tooltip: 'Khách hàng đã có tương tác sơ bộ, đủ điều kiện marketing chăm sóc'
    },
    {
      key: 'qualifiedCustomers',
      label: 'Khách hàng đủ điều kiện',
      color: '#4d4dff',
      tooltip: 'Khách hàng có nhu cầu thật, người ra quyết định, khả năng mua'
    },
    {
      key: 'inProgress',
      label: 'Khách hàng cơ hội',
      color: '#ff9900',
      tooltip: 'Khách hàng đang trong quá trình tư vấn, demo, đàm phán, gửi báo giá'
    },
    {
      key: 'officialCustomers',
      label: 'Khách hàng chính thức',
      color: '#0066ff',
      tooltip: 'Khách hàng đã ký hợp đồng, đang triển khai hoặc sử dụng dịch vụ'
    },
    {
      key: 'careAndDevelop',
      label: 'Chăm sóc và phát triển',
      color: '#009900',
      tooltip: 'Khách hàng lớn, đang khai thác thêm cơ hội bán hàng'
    },
    {
      key: 'failed',
      label: 'Khách hàng không thành công',
      color: '#ff3300',
      tooltip: 'Khách hàng đã rời đi, mất hợp đồng hoặc không còn tương tác dài hạn'
    }
  ]

  useEffect(() => {
    setData(fakeCustomerData.allCustomers)
  }, [])

  return (
    <BaseView>
      <Row>
        {stages.map(({ key, label, color, tooltip }) => (
          <Tooltip key={key} title={tooltip} color={color}>
            <Button
              onClick={() => handleClick(key, fakeCustomerData[key])}
              style={{
                margin: 10,
                height: 32,
                fontWeight: 'bold',
                backgroundColor: color,
                opacity: selectedStage === null || selectedStage === key ? 1 : 0.3,
                transition: 'opacity 0.3s'
              }}>
              {label}
            </Button>
          </Tooltip>
        ))}
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={data}
            total={0}
            isLoading={false}
            onPageChange={handlePageChange}
            scroll={{ x: 2000 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

export default ListCustomerView
