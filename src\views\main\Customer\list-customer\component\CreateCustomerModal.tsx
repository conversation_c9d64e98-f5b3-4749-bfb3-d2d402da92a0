import { SaveOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Upload,
  message,
  DatePicker,
  Radio,
  Checkbox,
  Space,
  Divider
} from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useState } from 'react'
import { toastService } from '~/services'
import type { UploadFile } from 'antd'
import useUploadSingle from '~/hooks/uploadFile/useUploadSingle'
import { useTranslation } from 'react-i18next'

const { Option } = Select
const { TextArea } = Input

interface CreateCustomerModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateCustomerModal = ({ open, onClose, onSuccess }: CreateCustomerModalProps) => {
  const [form] = useForm()
  const [fileList, setFileList] = useState<UploadFile[]>([])
  const { t } = useTranslation()

  const { mutateAsync: uploadSingle, isPending: isUploadingSingle } = useUploadSingle()

  const handleSave = async (values: any) => {
    if (!values) return

    const body = {
      ...values,
      images: fileList.map((file) => file.url)
    }

    try {
      // TODO: Implement create customer API
      onClose()
      onSuccess?.()
      form.resetFields()
      setFileList([])
    } catch (error) {
      toastService.error(t('customer:customer.customer_create.validation.createError'))
    }
  }

  const handleUploadChange = (res: any) => {
    if (res.Location) {
      setFileList((curr) => {
        return [
          ...curr,
          {
            uid: res.Location,
            name: res.Location,
            status: 'done',
            url: res.Location
          }
        ]
      })
    }
  }

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      message.error(t('customer:customer.customer_create.upload.imageOnly'))
      return false
    }

    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isLt5M) {
      message.error(t('customer:customer.customer_create.upload.sizeLimit'))
      return false
    }

    return true
  }

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>{t('customer:customer.customer_create.upload.button')}</div>
    </div>
  )

  const modalContent = (
    <Form form={form} layout='vertical' onFinish={handleSave}>
      {/* Thông tin cơ bản */}
      <Divider orientation='left'>
        {t('customer:customer.customer_create.sections.basic_info')}
      </Divider>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.market')}
            name='market'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.market')
                })
              }
            ]}>
            <Radio.Group>
              <Radio value='all'>Cả hai</Radio>
              <Radio value='domestic'>
                {t('customer:customer.customer_create.options.market.domestic')}
              </Radio>
              <Radio value='international'>
                {t('customer:customer.customer_create.options.market.international')}
              </Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        <Col span={12}>
          {/* <Form.Item
            label={t('customer:customer.customer_create.fields.isTopInvestor')}
            name='isTopInvestor'
            valuePropName='checked'>
            <Checkbox>{t('customer:customer.customer_create.checkboxes.isTopInvestor')}</Checkbox>
          </Form.Item> */}
          {/* Cần chăm sóc đặc biệt */}
          <Form.Item
            label={t('customer:customer.customer_create.fields.isSpecialCare')}
            name='isSpecialCare'
            valuePropName='checked'>
            <Checkbox>{t('customer:customer.customer_create.checkboxes.isSpecialCare')}</Checkbox>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.customerRank')}
            name='customerRank'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.customerRank')
                })
              }
            ]}>
            <Select placeholder={t('customer:customer.customer_create.placeholders.selectRank')}>
              <Option value='A'>
                {t('customer:customer.customer_create.options.customerRank.A')}
              </Option>
              <Option value='B'>
                {t('customer:customer.customer_create.options.customerRank.B')}
              </Option>
              <Option value='C'>
                {t('customer:customer.customer_create.options.customerRank.C')}
              </Option>
              <Option value='D'>
                {t('customer:customer.customer_create.options.customerRank.D')}
              </Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.customerSource')}
            name='customerSource'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.customerSource')
                })
              }
            ]}>
            <Select placeholder={t('customer:customer.customer_create.placeholders.selectSource')}>
              <Option value='website'>
                {t('customer:customer.customer_create.options.customerSource.website')}
              </Option>
              <Option value='facebook'>
                {t('customer:customer.customer_create.options.customerSource.facebook')}
              </Option>
              <Option value='google'>
                {t('customer:customer.customer_create.options.customerSource.google')}
              </Option>
              <Option value='referral'>
                {t('customer:customer.customer_create.options.customerSource.referral')}
              </Option>
              <Option value='other'>
                {t('customer:customer.customer_create.options.customerSource.other')}
              </Option>
            </Select>
          </Form.Item>
        </Col>
        {/* <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.activityRating')}
            name='activityRating'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.required', {
                  field: t('customer:customer.customer_create.fields.activityRating')
                })
              }
            ]}>
            <Input
              placeholder={t('customer:customer.customer_create.placeholders.activityRating')}
            />
          </Form.Item>
        </Col> */}
      </Row>

      <Row gutter={16}>
        {/* <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.customerSource')}
            name='customerSource'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.customerSource')
                })
              }
            ]}>
            <Select placeholder={t('customer:customer.customer_create.placeholders.selectSource')}>
              <Option value='website'>
                {t('customer:customer.customer_create.options.customerSource.website')}
              </Option>
              <Option value='facebook'>
                {t('customer:customer.customer_create.options.customerSource.facebook')}
              </Option>
              <Option value='google'>
                {t('customer:customer.customer_create.options.customerSource.google')}
              </Option>
              <Option value='referral'>
                {t('customer:customer.customer_create.options.customerSource.referral')}
              </Option>
              <Option value='other'>
                {t('customer:customer.customer_create.options.customerSource.other')}
              </Option>
            </Select>
          </Form.Item>
        </Col> */}
        {/* <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.branch')}
            name='branch'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.branch')
                })
              }
            ]}>
            <Select placeholder={t('customer:customer.customer_create.placeholders.selectBranch')}>
              <Option value='hcm'>
                {t('customer:customer.customer_create.options.branch.hcm')}
              </Option>
              <Option value='hn'>{t('customer:customer.customer_create.options.branch.hn')}</Option>
              <Option value='dn'>{t('customer:customer.customer_create.options.branch.dn')}</Option>
            </Select>
          </Form.Item>
        </Col> */}
      </Row>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.isProjectInvestor')}
            name='isProjectInvestor'
            valuePropName='checked'>
            <Checkbox>
              {t('customer:customer.customer_create.checkboxes.isProjectInvestor')}
            </Checkbox>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.isProjectDesigner')}
            name='isProjectDesigner'
            valuePropName='checked'>
            <Checkbox>
              {t('customer:customer.customer_create.checkboxes.isProjectDesigner')}
            </Checkbox>
          </Form.Item>
        </Col>
        {/* <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.isProjectContractor')}
            name='isProjectContractor'
            valuePropName='checked'>
            <Checkbox>
              {t('customer:customer.customer_create.checkboxes.isProjectContractor')}
            </Checkbox>
          </Form.Item>
        </Col> */}
      </Row>

      {/* Thông tin cá nhân */}
      <Divider orientation='left'>
        {t('customer:customer.customer_create.sections.personal_info')}
      </Divider>
      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.fullName')}
            name='fullName'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.required', {
                  field: t('customer:customer.customer_create.fields.fullName')
                })
              }
            ]}>
            <Input placeholder={t('customer:customer.customer_create.placeholders.fullName')} />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.shortName')}
            name='shortName'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.required', {
                  field: t('customer:customer.customer_create.fields.shortName')
                })
              }
            ]}>
            <Input placeholder={t('customer:customer.customer_create.placeholders.shortName')} />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.website')}
            name='website'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.required', {
                  field: t('customer:customer.customer_create.fields.website')
                })
              }
            ]}>
            <Input placeholder={t('customer:customer.customer_create.placeholders.website')} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.dateOfBirth')}
            name='dateOfBirth'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.dateOfBirth')
                })
              }
            ]}>
            <DatePicker style={{ width: '100%' }} format='DD/MM/YYYY' />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.gender')}
            name='gender'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.gender')
                })
              }
            ]}>
            <Select placeholder={t('customer:customer.customer_create.placeholders.selectGender')}>
              <Option value='male'>
                {t('customer:customer.customer_create.options.gender.male')}
              </Option>
              <Option value='female'>
                {t('customer:customer.customer_create.options.gender.female')}
              </Option>
              <Option value='other'>
                {t('customer:customer.customer_create.options.gender.other')}
              </Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.age')}
            name='age'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.required', {
                  field: t('customer:customer.customer_create.fields.age')
                })
              }
            ]}>
            <InputNumber style={{ width: '100%' }} min={0} max={150} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.phone')}
            name='phone'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.required', {
                  field: t('customer:customer.customer_create.fields.phone')
                })
              }
            ]}>
            <Input placeholder={t('customer:customer.customer_create.placeholders.phone')} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.email')}
            name='email'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.required', {
                  field: t('customer:customer.customer_create.fields.email')
                })
              },
              {
                type: 'email',
                message: t('customer:customer.customer_create.validation.emailInvalid')
              }
            ]}>
            <Input placeholder={t('customer:customer.customer_create.placeholders.email')} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.industry')}
            name='industry'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.industry')
                })
              }
            ]}>
            <Select
              placeholder={t('customer:customer.customer_create.placeholders.selectIndustry')}>
              <Option value='retail'>
                {t('customer:customer.customer_create.options.industry.retail')}
              </Option>
              <Option value='wholesale'>
                {t('customer:customer.customer_create.options.industry.wholesale')}
              </Option>
              <Option value='service'>
                {t('customer:customer.customer_create.options.industry.service')}
              </Option>
              <Option value='other'>
                {t('customer:customer.customer_create.options.industry.other')}
              </Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.nationality')}
            name='nationality'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.nationality')
                })
              }
            ]}>
            <Select
              placeholder={t('customer:customer.customer_create.placeholders.selectNationality')}>
              <Option value='vn'>
                {t('customer:customer.customer_create.options.nationality.vn')}
              </Option>
              <Option value='us'>
                {t('customer:customer.customer_create.options.nationality.us')}
              </Option>
              <Option value='uk'>
                {t('customer:customer.customer_create.options.nationality.uk')}
              </Option>
              <Option value='other'>
                {t('customer:customer.customer_create.options.nationality.other')}
              </Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      {/* Thông tin địa chỉ */}
      <Divider orientation='left'>
        {t('customer:customer.customer_create.sections.address_info')}
      </Divider>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.addressType')}
            name='addressType'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.addressType')
                })
              }
            ]}>
            <Select
              placeholder={t('customer:customer.customer_create.placeholders.selectAddressType')}>
              <Option value='home'>
                {t('customer:customer.customer_create.options.addressType.home')}
              </Option>
              <Option value='office'>
                {t('customer:customer.customer_create.options.addressType.office')}
              </Option>
              <Option value='other'>
                {t('customer:customer.customer_create.options.addressType.other')}
              </Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.region')}
            name='region'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.region')
                })
              }
            ]}>
            <Select placeholder={t('customer:customer.customer_create.placeholders.selectRegion')}>
              <Option value='north'>
                {t('customer:customer.customer_create.options.region.north')}
              </Option>
              <Option value='central'>
                {t('customer:customer.customer_create.options.region.central')}
              </Option>
              <Option value='south'>
                {t('customer:customer.customer_create.options.region.south')}
              </Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.city')}
            name='city'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.city')
                })
              }
            ]}>
            <Select placeholder={t('customer:customer.customer_create.placeholders.selectCity')}>
              <Option value='hcm'>{t('customer:customer.customer_create.options.city.hcm')}</Option>
              <Option value='hn'>{t('customer:customer.customer_create.options.city.hn')}</Option>
              <Option value='dn'>{t('customer:customer.customer_create.options.city.dn')}</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.district')}
            name='district'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.district')
                })
              }
            ]}>
            <Select
              placeholder={t('customer:customer.customer_create.placeholders.selectDistrict')}>
              <Option value='q1'>
                {t('customer:customer.customer_create.options.district.q1')}
              </Option>
              <Option value='q2'>
                {t('customer:customer.customer_create.options.district.q2')}
              </Option>
              <Option value='q3'>
                {t('customer:customer.customer_create.options.district.q3')}
              </Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.ward')}
            name='ward'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.ward')
                })
              }
            ]}>
            <Select placeholder={t('customer:customer.customer_create.placeholders.selectWard')}>
              <Option value='p1'>{t('customer:customer.customer_create.options.ward.p1')}</Option>
              <Option value='p2'>{t('customer:customer.customer_create.options.ward.p2')}</Option>
              <Option value='p3'>{t('customer:customer.customer_create.options.ward.p3')}</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={16}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.address')}
            name='address'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.required', {
                  field: t('customer:customer.customer_create.fields.address')
                })
              }
            ]}>
            <Input placeholder={t('customer:customer.customer_create.placeholders.address')} />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.area')}
            name='area'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.required', {
                  field: t('customer:customer.customer_create.fields.area')
                })
              }
            ]}>
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              addonAfter='m²'
              placeholder={t('customer:customer.customer_create.placeholders.area')}
            />
          </Form.Item>
        </Col>
      </Row>

      {/* Thông tin bổ sung */}
      <Divider orientation='left'>
        {t('customer:customer.customer_create.sections.additional_info')}
      </Divider>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.visitDate')}
            name='visitDate'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.visitDate')
                })
              }
            ]}>
            <DatePicker style={{ width: '100%' }} format='DD/MM/YYYY' />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.salesRep')}
            name='salesRep'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.salesRep')
                })
              }
            ]}>
            <Select
              placeholder={t('customer:customer.customer_create.placeholders.selectSalesRep')}>
              <Option value='nv1'>
                {t('customer:customer.customer_create.options.salesRep.nv1')}
              </Option>
              <Option value='nv2'>
                {t('customer:customer.customer_create.options.salesRep.nv2')}
              </Option>
              <Option value='nv3'>
                {t('customer:customer.customer_create.options.salesRep.nv3')}
              </Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.mainProductGroup')}
            name='mainProductGroup'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.mainProductGroup')
                })
              }
            ]}>
            <Select
              placeholder={t('customer:customer.customer_create.placeholders.selectProductGroup')}
              mode='multiple'>
              <Option value='group1'>
                {t('customer:customer.customer_create.options.productGroup.group1')}
              </Option>
              <Option value='group2'>
                {t('customer:customer.customer_create.options.productGroup.group2')}
              </Option>
              <Option value='group3'>
                {t('customer:customer.customer_create.options.productGroup.group3')}
              </Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.transactionYear')}
            name='transactionYear'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.transactionYear')
                })
              }
            ]}>
            <DatePicker picker='year' style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.signboardCount')}
            name='signboardCount'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.required', {
                  field: t('customer:customer.customer_create.fields.signboardCount')
                })
              }
            ]}>
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              placeholder={t('customer:customer.customer_create.placeholders.signboardCount')}
            />
          </Form.Item>
        </Col> */}
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.productTrustLevel')}
            name='productTrustLevel'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.productTrustLevel')
                })
              }
            ]}>
            <Select
              placeholder={t('customer:customer.customer_create.placeholders.selectTrustLevel')}>
              <Option value='high'>
                {t('customer:customer.customer_create.options.trustLevel.high')}
              </Option>
              <Option value='medium'>
                {t('customer:customer.customer_create.options.trustLevel.medium')}
              </Option>
              <Option value='low'>
                {t('customer:customer.customer_create.options.trustLevel.low')}
              </Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.interactionLevel')}
            name='interactionLevel'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.interactionLevel')
                })
              }
            ]}>
            <Select
              placeholder={t(
                'customer:customer.customer_create.placeholders.selectInteractionLevel'
              )}>
              <Option value='high'>
                {t('customer:customer.customer_create.options.interactionLevel.high')}
              </Option>
              <Option value='medium'>
                {t('customer:customer.customer_create.options.interactionLevel.medium')}
              </Option>
              <Option value='low'>
                {t('customer:customer.customer_create.options.interactionLevel.low')}
              </Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.productAwareness')}
            name='productAwareness'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.productAwareness')
                })
              }
            ]}>
            <Select
              placeholder={t('customer:customer.customer_create.placeholders.selectAwareness')}>
              <Option value='website'>
                {t('customer:customer.customer_create.options.productAwareness.website')}
              </Option>
              <Option value='social'>
                {t('customer:customer.customer_create.options.productAwareness.social')}
              </Option>
              <Option value='friend'>
                {t('customer:customer.customer_create.options.productAwareness.friend')}
              </Option>
              <Option value='other'>
                {t('customer:customer.customer_create.options.productAwareness.other')}
              </Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.investmentSegment')}
            name='investmentSegment'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.investmentSegment')
                })
              }
            ]}>
            <Select
              placeholder={t(
                'customer:customer.customer_create.placeholders.selectInvestmentSegment'
              )}>
              <Option value='high'>
                {t('customer:customer.customer_create.options.investmentSegment.high')}
              </Option>
              <Option value='medium'>
                {t('customer:customer.customer_create.options.investmentSegment.medium')}
              </Option>
              <Option value='low'>
                {t('customer:customer.customer_create.options.investmentSegment.low')}
              </Option>
            </Select>
          </Form.Item>
        </Col> */}
        {/* <Col span={12}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.productAwareness')}
            name='productAwareness'
            rules={[
              {
                required: true,
                message: t('customer:customer.customer_create.validation.selectRequired', {
                  field: t('customer:customer.customer_create.fields.productAwareness')
                })
              }
            ]}>
            <Select
              placeholder={t('customer:customer.customer_create.placeholders.selectAwareness')}>
              <Option value='website'>
                {t('customer:customer.customer_create.options.productAwareness.website')}
              </Option>
              <Option value='social'>
                {t('customer:customer.customer_create.options.productAwareness.social')}
              </Option>
              <Option value='friend'>
                {t('customer:customer.customer_create.options.productAwareness.friend')}
              </Option>
              <Option value='other'>
                {t('customer:customer.customer_create.options.productAwareness.other')}
              </Option>
            </Select>
          </Form.Item>
        </Col> */}
      </Row>

      {/* <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label={t('customer:customer.customer_create.fields.shoppingHabits')}
            name='shoppingHabits'>
            <Checkbox.Group>
              <Space direction='vertical'>
                <Checkbox value='promotion'>
                  {t('customer:customer.customer_create.options.shoppingHabits.promotion')}
                </Checkbox>
                <Checkbox value='noInterest'>
                  {t('customer:customer.customer_create.options.shoppingHabits.noInterest')}
                </Checkbox>
              </Space>
            </Checkbox.Group>
          </Form.Item>
        </Col>
      </Row> */}

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label={t('customer:customer.customer_create.fields.note')} name='note'>
            <TextArea
              rows={4}
              placeholder={t('customer:customer.customer_create.placeholders.note')}
            />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label={t('customer:customer.customer_create.fields.images')} name='images'>
        <Upload
          listType='picture-card'
          fileList={fileList}
          onChange={handleUploadChange}
          beforeUpload={beforeUpload}
          multiple
          accept='image/*'
          customRequest={async ({ file, onSuccess }) => {
            try {
              const formData = new FormData()
              formData.append('file', file as File)
              await uploadSingle(formData).then((res) => {
                handleUploadChange(res)
              })
            } catch (error) {}
          }}>
          {fileList.length >= 8 ? null : uploadButton}
        </Upload>
      </Form.Item>

      <div
        style={{
          textAlign: 'right',
          marginTop: 24,
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16
        }}>
        <Button onClick={onClose} style={{ marginRight: 8 }}>
          {t('customer:customer.customer_create.cancel')}
        </Button>
        <Button
          type='primary'
          htmlType='submit'
          icon={<SaveOutlined />}
          loading={isUploadingSingle}>
          {t('customer:customer.customer_create.create')}
        </Button>
      </div>
    </Form>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title={t('customer:customer.customer_create.title')}
      description={t('customer:customer.customer_create.description')}
      childrenBody={modalContent}
    />
  )
}

export default CreateCustomerModal
